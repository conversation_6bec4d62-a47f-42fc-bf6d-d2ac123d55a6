#!/bin/bash
set -e

# 腾讯云 CVM User Data 脚本 - 安装和配置 Coder Agent

export DEBIAN_FRONTEND=noninteractive

echo "🏞️ 更换为腾讯云内网软件源..."
cat <<EOF | tee /etc/apt/sources.list.d/debian.sources
Types: deb deb-src
URIs: http://mirrors.tencentyun.com/debian
Suites: trixie trixie-updates trixie-backports
Components: main contrib non-free non-free-firmware
Signed-By: /usr/share/keyrings/debian-archive-keyring.gpg

Types: deb deb-src
URIs: http://mirrors.tencentyun.com/debian-security
Suites: trixie-security
Components: main contrib non-free non-free-firmware
Signed-By: /usr/share/keyrings/debian-archive-keyring.gpg
EOF

echo "🚀 开始配置 Coder Agent..."
echo "📦 安装初始化所必须的依赖..."
apt-get update -qq
apt-get install -y -qq \
    curl \
    git \
    jq

echo "📁 创建 Coder 目录..."
mkdir -p /opt/coder

echo "📝 写入 Coder Agent 初始化脚本..."
cat > /opt/coder/init << 'EOF'
${base64decode(init_script)}
EOF

# 设置执行权限
chmod +x /opt/coder/init

echo "⚙️ 创建 Coder Agent systemd 服务..."
cat > /etc/systemd/system/coder-agent.service << 'EOF'
[Unit]
Description=Coder Agent
After=network-online.target
Wants=network-online.target

[Service]
User=root
ExecStart=/opt/coder/init
Environment=CODER_AGENT_TOKEN=${coder_agent_token}
Restart=always
RestartSec=10
TimeoutStopSec=90
KillMode=process

OOMScoreAdjust=-900
SyslogIdentifier=coder-agent

[Install]
WantedBy=multi-user.target
EOF

echo "🔄 重新加载 systemd 配置..."
systemctl daemon-reload

echo "✅ 启用 Coder Agent 服务..."
systemctl enable coder-agent

echo "🚀 启动 Coder Agent 服务..."
systemctl start coder-agent

echo "🎉 Coder Agent 配置完成!"
