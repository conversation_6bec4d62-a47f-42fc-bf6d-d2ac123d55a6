terraform {
  required_providers {
    tencentcloud = {
      source  = "tencentcloudstack/tencentcloud"
      version = "1.82.26"
    }
    coder = {
      source  = "coder/coder"
      version = "2.11.0"
    }
  }
}

# 配置腾讯云 Provider
provider "tencentcloud" {
  secret_id  = var.tencentcloud_cvm_secret_id
  secret_key = var.tencentcloud_cvm_secret_key
  region     = local.region
}

# 配置 Coder Provider
provider "coder" {}

# 配置 Coder Agent
resource "coder_agent" "main" {
  arch = "amd64" # 目标架构
  os   = "linux" # 目标操作系统

  # 配置 Coder Web UI 中显示的基础工具
  display_apps {
    vscode                 = false # 禁用 VSCode
    vscode_insiders        = false # 禁用 VSCode Insiders
    web_terminal           = true  # 启用 Web 终端
    ssh_helper             = true  # 启用 SSH 助手
    port_forwarding_helper = true  # 启用端口转发助手
  }

  # 执行内联脚本
  startup_script = <<-EOT
    #!/bin/bash
    set -e

    # 等待 Git 模块配置完成
    # 设置倒计时秒数
    seconds=5
    # 开始倒计时
    echo "⏰ 请等待以确保 Git 模块配置完成..."
    for (( i=$seconds; i>0; i-- )); do
      echo "⏳ 还有 $i 秒..."
      sleep 1
    done

    echo "🔑 为 Git 配置全局 SSH 私钥..."
    /usr/bin/git config --global core.sshCommand "ssh -i ~/.ssh/git-commit-signing/coder -o IdentitiesOnly=yes"

    echo "👤 为 Git 配置全局用户信息..."
    # 从 Coder 获取用户信息并设置为 Git 全局用户名和邮箱
    GIT_USER_NAME="${coalesce(data.coder_workspace_owner.me.full_name, data.coder_workspace_owner.me.name)}"
    GIT_USER_EMAIL="${data.coder_workspace_owner.me.email}"
    /usr/bin/git config --global user.name "$GIT_USER_NAME"
    /usr/bin/git config --global user.email "$GIT_USER_EMAIL"

    echo "✅ Git 用户配置完成:"
    echo "   用户名: $GIT_USER_NAME"
    echo "   邮箱: $GIT_USER_EMAIL"

    echo "🚀 开始初始化 Coder 工作空间..."
    echo "🔧 第 (1/2) 阶段: 配置系统开发环境..."
    echo "📦 更新系统包管理器和环境..."
    export DEBIAN_FRONTEND=noninteractive
    apt-get upgrade -y -qq

    echo "🛠️ 安装基础系统工具..."
    apt-get install -y -qq \
        ca-certificates \
        gnupg \
        lsb-release

    echo "🔨 安装开发基础工具..."
    apt-get install -y -qq \
        gcc \
        g++ \
        build-essential \
        pkg-config \
        libssl-dev \
        unzip \
        zip \
        btop \
        tree \
        pigz \
        nano \
        make \
        flex \
        bison \
        libboost-all-dev \
        bc \
        fakeroot \
        dwarves \
        libelf-dev \
        lz4
    apt-get build-dep linux -qq

    echo "🌐 安装网络和调试工具..."
    apt-get install -y -qq \
        net-tools \
        iputils-ping \
        telnet \
        dnsutils \
        tcpdump \
        strace \
        lsof

    echo "🦀 安装 Rust 开发环境..."
    apt-get install -y cpanminus
    cpanm -q --notest FindBin
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    ~/.cargo/bin/cargo install cross
    ~/.cargo/bin/rustup component add rust-analyzer

    echo "📦 安装 Node.js 开发环境..."
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash -
    apt-get install -y nodejs

    echo "🐍 安装 Python 开发环境..."
    apt-get install -y -qq \
        python3 \
        python3-pip \
        python3-venv \
        python3-dev

    echo "🐳 安装 Docker..."
    curl -fsSL https://get.docker.com | sh
    systemctl enable docker
    systemctl start docker

    echo "🧹 清理系统缓存..."
    apt-get autoremove -y -qq
    apt-get autoclean -qq

    echo "✅ 系统环境配置完成!"

    echo "💾 第 (2/2) 阶段: 配置 JuiceFS 云服务..."
    # 设置 JuiceFS 参数为环境变量
    export FILESYSTEM_NAME="${var.juicefs_vol_name}"
    export JUICEFS_TOKEN="${var.juicefs_token}"
    export COS_ACCESS_KEY="${var.tencentcloud_cos_secret_id}"
    export COS_SECRET_KEY="${var.tencentcloud_cos_secret_key}"
    export COS_ENDPOINT="${var.tencentcloud_cos_endpoint}"

    echo "📋 JuiceFS 配置参数:"
    echo "   文件系统名称: $FILESYSTEM_NAME"
    echo "   COS Endpoint: $COS_ENDPOINT"

    echo "📥 开始安装 JuiceFS..."
    curl -sSL https://juicefs.com/static/juicefs -o /usr/local/bin/juicefs && chmod +x /usr/local/bin/juicefs

    echo "✅ JuiceFS 安装成功"
    /usr/local/bin/juicefs version

    echo "🔐 配置 JuiceFS 认证..."
    /usr/local/bin/juicefs auth "$FILESYSTEM_NAME" \
        --token "$JUICEFS_TOKEN" \
        --accesskey "$COS_ACCESS_KEY" \
        --secretkey "$COS_SECRET_KEY" \
        --bucket "$COS_ENDPOINT"

    echo "✅ JuiceFS 认证成功"

    echo "📁 创建工作空间挂载点..."
    mkdir -p /data/workspaces

    echo "🔗 挂载 JuiceFS 文件系统..."
    /usr/local/bin/juicefs mount "$FILESYSTEM_NAME" /data/workspaces \
        --update-fstab \
        --cache-size=10240

    # 等待挂载完成
    sleep 3

    # 验证挂载状态
    if mountpoint -q /data/workspaces; then
        echo "✅ JuiceFS 文件系统挂载成功"
    else
        echo "❌ JuiceFS 文件系统挂载失败"
        exit 1
    fi

    # 打印初始化完成信息
    echo ""
    echo "🎉 Coder 工作空间初始化已完成, Enjoy!"
  EOT

  # 元数据配置
  metadata {
    display_name = "JuiceFS"
    key          = "juicefs_status"
    script       = "mountpoint -q /data/workspaces && echo 'Ready' || echo 'Not ready'"
    interval     = 10
    timeout      = 1
    order        = 1
  }
  metadata {
    display_name = "CPU"
    key          = "cpu_usage"
    script       = "coder stat cpu"
    interval     = 10
    timeout      = 1
    order        = 2
  }
  metadata {
    display_name = "RAM"
    key          = "ram_usage"
    script       = "coder stat mem"
    interval     = 10
    timeout      = 1
    order        = 3
  }
  metadata {
    display_name = "System Disk"
    key          = "system_disk_usage"
    script       = "coder stat disk"
    interval     = 10
    timeout      = 1
    order        = 4
  }
  metadata {
    display_name = "JuiceFS Disk"
    key          = "juicefs_disk_usage"
    script       = "coder stat disk --path /data/workspaces"
    interval     = 10
    timeout      = 1
    order        = 5
  }
}

# 实例信息元数据显示
resource "coder_metadata" "instance_info" {
  count       = data.coder_workspace.me.start_count
  resource_id = tencentcloud_instance.coder_instance.id

  item {
    key   = "Instance Type"
    value = tencentcloud_instance.coder_instance.instance_type
  }

  item {
    key   = "Charge Type"
    value = tencentcloud_instance.coder_instance.instance_charge_type
  }

  item {
    key   = "OS Name"
    value = tencentcloud_instance.coder_instance.os_name
  }

  item {
    key   = "System Disk Type"
    value = tencentcloud_instance.coder_instance.system_disk_type
  }

  item {
    key   = "Zone"
    value = tencentcloud_instance.coder_instance.availability_zone
  }

  item {
    key   = "Public IP"
    value = tencentcloud_instance.coder_instance.public_ip
  }

  item {
    key   = "Private IP"
    value = tencentcloud_instance.coder_instance.private_ip
  }
}

# =================== code-server 模块配置 ===================

module "code-server" {
  count    = data.coder_workspace.me.start_count
  source   = "registry.coder.com/coder/code-server/coder"
  version  = "1.3.1"
  agent_id = coder_agent.main.id
  order    = 1
}

# ==================== JetBrains 模块配置 ====================

module "jetbrains" {
  count      = data.coder_workspace.me.start_count
  source     = "registry.coder.com/coder/jetbrains/coder"
  version    = "1.0.3"
  agent_id   = coder_agent.main.id
  agent_name = "main"
  folder     = "/data/workspaces"
  default    = ["CL", "RR", "PY", "WS"] # CLion, RustRover, PyCharm, WebStorm
  coder_app_order = 2
}

# ==================== Git 模块配置 ====================

module "git-commit-signing" {
  count    = data.coder_workspace.me.start_count
  source   = "registry.coder.com/coder/git-commit-signing/coder"
  version  = "1.0.31"
  agent_id = coder_agent.main.id
}

# ==================== 启动腾讯云 CVM 实例 ====================

# 创建 CVM 实例
resource "tencentcloud_instance" "coder_instance" {
  # 基本配置
  instance_name     = "coder-${data.coder_workspace_owner.me.name}-${data.coder_workspace.me.name}"
  availability_zone = local.availability_zone
  image_id          = local.image_id
  instance_type     = data.coder_parameter.instance_type.value
  hostname          = "coder-${substr(data.coder_workspace.me.id, 0, 8)}"
  project_id        = local.project_id

  # 计费配置
  instance_charge_type = data.coder_parameter.instance_charge_type.value

  # 竞价实例配置 (选择竞价实例时使用用户自定义价格)
  spot_instance_type = data.coder_parameter.instance_charge_type.value == "SPOTPAID" ? "ONE-TIME" : null
  spot_max_price     = data.coder_parameter.instance_charge_type.value == "SPOTPAID" ? data.coder_parameter.spot_max_price.value : null

  # 网络配置
  vpc_id                     = local.vpc_id
  subnet_id                  = local.subnet_id
  orderly_security_groups    = [local.security_group_id]
  allocate_public_ip         = local.allocate_public_ip
  internet_charge_type       = local.internet_charge_type
  internet_max_bandwidth_out = local.internet_max_bandwidth_out

  # 存储配置
  system_disk_type = local.system_disk_type
  system_disk_size = local.system_disk_size

  # SSH 密钥配置
  key_ids = [local.ssh_key_id]

  # 用户数据脚本 - 安装 Coder Agent (Shell 脚本格式 + Base64 编码以符合腾讯云 CVM 要求)
  user_data = base64encode(templatefile("user-data.sh.tftpl", {
    init_script       = base64encode(coder_agent.main.init_script)
    coder_agent_token = coder_agent.main.token
  }))

  # 服务配置 (按要求禁用相关服务)
  disable_security_service   = true # 不启用安全加固
  disable_monitor_service    = true # 不启用云监控
  disable_automation_service = true # 不启用自动化助手

  # 标签配置
  tags = {
    "Coder"        = "true"
    "Owner"        = data.coder_workspace_owner.me.name
    "Workspace"    = data.coder_workspace.me.name
    "InstanceType" = data.coder_parameter.instance_type.value
    "ChargeType"   = data.coder_parameter.instance_charge_type.value
    "Environment"  = "Development"
    "CreatedBy"    = "Terraform"
  }
}
