# Coder 腾讯云工作空间模板

## 概述

这是一个用于在腾讯云上创建 Coder 工作空间的 Terraform 模板。该模板提供了完整的开发环境配置，包括 JuiceFS 云存储集成、多种开发工具预装以及 JetBrains 远程开发支持。

## 功能特性

### 🚀 核心功能
- **操作系统**: 使用 Debian 13 自定义镜像，预装完整开发环境
- **灵活存储**: 支持高性能云硬盘和增强型SSD云硬盘两种存储类型
- **计费模式**: 支持竞价实例和按量计费两种模式
- **JuiceFS 集成**: 自动配置 JuiceFS 云存储，提供持久化工作空间
- **JetBrains 支持**: 预配置 CLion、RustRover、PyCharm、WebStorm 等 IDE

### 🛠️ 预装开发环境
- **系统工具**: gcc, g++, git, build-essential, nano, btop, tree, jq 等
- **网络工具**: net-tools, ping, telnet, tcpdump, strace, lsof 等
- **开发语言**: 
  - Rust (包含 cross 和 rust-analyzer)
  - Node.js (LTS 版本)
  - Python 3 (包含 pip, venv, dev)
- **容器化**: Docker CE

## 用户可配置参数

### 1. 计费模式 (instance_charge_type)
- **竞价实例** (SPOTPAID): 成本更低，但可能被回收
- **按量计费** (POSTPAID_BY_HOUR): 稳定可靠，按实际使用计费

### 2. 实例规格 (instance_type)
- **标准型 S5.LARGE8**: 4核8GB，适合一般开发
- **计算型 C4.2XLARGE16**: 8核16GB，适合计算密集型任务

### 3. 竞价实例最高出价 (spot_max_price)
- 仅在选择竞价实例时生效
- 默认值: 0.048 元/小时
- 建议参考腾讯云 CVM 购买页的实时价格

### 4. 操作系统镜像 (image_id) 🆕
- **Debian 13 (自定义镜像)**: img-5s7vueks (固定)

### 5. 系统盘类型 (system_disk_type) 🆕
- **高性能云硬盘 (CLOUD_PREMIUM)**: 性价比高，适合一般使用 (默认)
- **增强型SSD云硬盘 (CLOUD_HSSD)**: 高性能，适合I/O密集型应用

## 元数据显示

工作空间创建后，在 Coder 界面中会显示以下信息：

### 系统监控
- **JuiceFS 状态**: 显示云存储挂载状态
- **CPU 使用率**: 实时 CPU 使用情况
- **内存使用率**: 实时内存使用情况
- **系统盘使用率**: 本地系统盘使用情况
- **JuiceFS 磁盘使用率**: 云存储使用情况

### 网络信息
- **公网 IP**: 实例的公网访问地址
- **私网 IP**: 实例的内网地址

### 配置信息 🆕
- **系统盘类型**: 显示用户选择的磁盘类型

## 环境变量配置

使用前需要配置以下环境变量：

```bash
# 腾讯云 CVM 认证
export TF_VAR_tencentcloud_cvm_secret_id="your_secret_id"
export TF_VAR_tencentcloud_cvm_secret_key="your_secret_key"

# JuiceFS 配置
export TF_VAR_juicefs_vol_name="coder-code"
export TF_VAR_juicefs_token="your_juicefs_token"

# 腾讯云 COS 配置
export TF_VAR_tencentcloud_cos_secret_id="your_cos_secret_id"
export TF_VAR_tencentcloud_cos_secret_key="your_cos_secret_key"
export TF_VAR_tencentcloud_cos_endpoint="http://your-bucket.cos.region.myqcloud.com"
```

## 使用方法

1. **初始化 Terraform**:
   ```bash
   terraform init
   ```

2. **验证配置**:
   ```bash
   terraform plan
   ```

3. **部署模板**:
   ```bash
   terraform apply
   ```

4. **在 Coder 中使用**:
   - 将生成的模板导入到 Coder 中
   - 创建工作空间时选择所需的配置参数
   - 等待工作空间初始化完成

## 网络和安全配置

- **地域**: 香港地域 (ap-hongkong)
- **可用区**: 香港二区 (ap-hongkong-2)
- **网络**: 使用预配置的 VPC 和子网
- **安全组**: 预配置的安全组规则
- **公网访问**: 自动分配公网 IP，按流量计费，200Mbps 带宽上限

## 注意事项

1. **竞价实例风险**: 选择竞价实例时，实例可能因价格波动被回收
2. **数据持久化**: 重要数据请保存在 JuiceFS 挂载的 `/data/workspaces` 目录
3. **成本控制**: 建议定期检查实例使用情况，及时停止不需要的工作空间
4. **镜像兼容性**: 不同镜像的软件包和配置可能有差异，请根据需要调整
5. **存储性能**: CLOUD_HSSD 性能更高但成本也更高，请根据实际需求选择

## 更新日志

### v2.0.0 (最新)
- 🆕 新增镜像ID参数化，使用 Debian 13 自定义镜像
- 🆕 新增系统盘类型参数化，支持高性能和增强型SSD选择
- 🆕 新增配置信息元数据显示，显示操作系统和系统盘类型信息
- ✅ 保持向后兼容性，默认配置不变

### v1.0.0
- 初始版本，支持基础的 Coder 工作空间创建
- 集成 JuiceFS 云存储
- 预装开发环境和工具
