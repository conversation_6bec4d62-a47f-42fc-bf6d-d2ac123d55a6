# Coder 模块等待机制优化说明

## 问题背景

在原始的 Coder 模板中，使用了硬编码的 5 秒等待时间来确保 `code-server` 和 `git-commit-signing` 模块配置完成：

```bash
# 原始代码（不优雅）
seconds=5
echo "⏰ 请等待以确保 Git 模块配置完成..."
for (( i=$seconds; i>0; i-- )); do
  echo "⏳ 还有 $i 秒..."
  sleep 1
done
```

这种方式存在以下问题：
- **不可靠**：固定等待时间可能不够或过长
- **不高效**：无论模块是否已完成都要等待固定时间
- **缺乏反馈**：无法知道具体哪个模块还未完成

## 解决方案

### 智能等待函数

新的解决方案使用智能检测函数 `wait_for_modules_completion()`，该函数会：

1. **动态检测**：实时检查模块配置状态
2. **状态反馈**：显示每个模块的完成状态
3. **超时保护**：设置最大等待时间（60秒）防止无限等待
4. **高效执行**：一旦检测到完成立即继续

### 检测逻辑

#### Git Commit Signing 模块检测
检查三个关键的 git 全局配置（这些正是模块脚本设置的核心配置）：
```bash
# 检查 GPG 格式设置
[ "$(git config --global gpg.format 2>/dev/null)" = "ssh" ]

# 检查提交签名启用状态
[ "$(git config --global commit.gpgsign 2>/dev/null)" = "true" ]

# 检查签名密钥路径
[ "$(git config --global user.signingkey 2>/dev/null)" = "~/.ssh/git-commit-signing/coder" ]
```

#### Code-Server 模块检测
检查 code-server 进程是否已启动运行：
```bash
# 检查 code-server 进程是否运行
pgrep -f "code-server" > /dev/null 2>&1
```

### 函数特性

- **最大等待时间**：60 秒
- **检查间隔**：2 秒
- **状态显示**：实时显示每个模块的状态（✅ 完成 / ⏳ 等待中）
- **超时处理**：超时后会显示警告但继续执行

## 使用效果

### 成功场景
```
⏰ 等待 Coder 模块配置完成...
⏳ 等待中... Git模块: ⏳ | Code-Server模块: ⏳
⏳ 等待中... Git模块: ✅ | Code-Server模块: ⏳
✅ 所有 Coder 模块配置完成！
```

### 超时场景
```
⏰ 等待 Coder 模块配置完成...
⏳ 等待中... Git模块: ⏳ | Code-Server模块: ⏳
...
⚠️ 等待超时（60秒），但将继续执行...
```

## 优势对比

| 特性 | 原始方案 | 优化方案 |
|------|----------|----------|
| 等待时间 | 固定 5 秒 | 动态检测，最快 2 秒 |
| 可靠性 | 低（可能不够或过长） | 高（基于实际状态） |
| 反馈信息 | 简单倒计时 | 详细状态显示 |
| 效率 | 低（总是等待固定时间） | 高（完成即继续） |
| 容错性 | 无 | 有超时保护 |

## 技术实现

新的智能等待函数已集成到 `main.tf` 文件的 `startup_script` 中，替换了原有的硬编码等待逻辑。

该函数基于对官方模块运行脚本的深入分析：
- [code-server/run.sh](https://raw.githubusercontent.com/coder/registry/refs/heads/main/registry/coder/modules/code-server/run.sh)
- [git-commit-signing/run.sh](https://raw.githubusercontent.com/coder/registry/refs/heads/main/registry/coder/modules/git-commit-signing/run.sh)

通过分析这些脚本的关键操作和输出文件，确定了最可靠的检测点。
